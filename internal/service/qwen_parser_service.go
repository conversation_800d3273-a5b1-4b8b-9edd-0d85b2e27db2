package service

import (
	"encoding/json"
	"regexp"
	"strings"

	"zhengshi/internal/model"
	"zhengshi/internal/utils"
)

// QwenParserService Qwen数据解析服务接口
type QwenParserService interface {
	ParseQwenData(rawData string, traceID string) (*QwenParsedResult, error)
	GenerateCacheKey(questType, questContent string, questOptions map[string]string, traceID string) (string, error)
}

// qwenParserService Qwen数据解析服务实现
type qwenParserService struct {
	logger utils.Logger
}

// QwenParsedResult Qwen解析结果
type QwenParsedResult struct {
	QuestType    string            `json:"quest_type"`    // 题目类型
	QuestOption  map[string]string `json:"quest_option"`  // 题目选项
	QuestContent string            `json:"quest_content"` // 题目正文
}

// QwenResponseContent Qwen响应内容结构（用于解析）
type QwenResponseContent struct {
	Qutext  string            `json:"qutext"`  // 题目文本
	Options map[string]string `json:"options"` // 选项
}

// NewQwenParserService 创建Qwen数据解析服务
func NewQwenParserService(logger utils.Logger) QwenParserService {
	return &qwenParserService{
		logger: logger,
	}
}

// ParseQwenData 解析Qwen返回的数据
func (s *qwenParserService) ParseQwenData(rawData string, traceID string) (*QwenParsedResult, error) {
	s.logger.Info("Starting Qwen data parsing",
		"trace_id", traceID,
		"raw_data_length", len(rawData),
	)

	// 1. 解析Qwen原始响应，提取content
	content, err := s.extractContentFromRawData(rawData, traceID)
	if err != nil {
		return nil, err
	}

	// 临时调试：打印Qwen返回的原始content
	s.logger.Info("DEBUG: Qwen raw content",
		"trace_id", traceID,
		"content", content,
		"content_length", len(content),
	)

	// 2. 解析content中的JSON数据
	var qwenContent QwenResponseContent
	if err := json.Unmarshal([]byte(content), &qwenContent); err != nil {
		s.logger.Error("Failed to parse Qwen content JSON", err,
			"trace_id", traceID,
			"content", content,
		)
		return nil, utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	s.logger.Info("Qwen content parsed successfully",
		"trace_id", traceID,
		"qutext", qwenContent.Qutext,
		"options_count", len(qwenContent.Options),
	)

	// 3. 提取题目类型
	questType, err := s.extractQuestType(qwenContent.Qutext, traceID)
	if err != nil {
		return nil, err
	}

	// 4. 验证和提取选项
	questOptions, err := s.validateAndExtractOptions(qwenContent.Options, questType, traceID)
	if err != nil {
		return nil, err
	}

	// 5. 清洗题目正文
	questContent, err := s.cleanQuestContent(qwenContent.Qutext, traceID)
	if err != nil {
		return nil, err
	}

	result := &QwenParsedResult{
		QuestType:    questType,
		QuestOption:  questOptions,
		QuestContent: questContent,
	}

	s.logger.Info("Qwen data parsing completed successfully",
		"trace_id", traceID,
		"quest_type", questType,
		"quest_content", questContent,
		"options_count", len(questOptions),
	)

	return result, nil
}

// extractContentFromRawData 从原始数据中提取content
func (s *qwenParserService) extractContentFromRawData(rawData string, traceID string) (string, error) {
	var dashScopeResponse model.QwenDashScopeResponse
	if err := json.Unmarshal([]byte(rawData), &dashScopeResponse); err != nil {
		s.logger.Error("Failed to parse DashScope response", err,
			"trace_id", traceID,
		)
		return "", utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	if len(dashScopeResponse.Output.Choices) == 0 {
		s.logger.Error("No choices in DashScope response", nil,
			"trace_id", traceID,
		)
		return "", utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	choice := dashScopeResponse.Output.Choices[0]
	if len(choice.Message.Content) == 0 {
		s.logger.Error("No content in DashScope response message", nil,
			"trace_id", traceID,
		)
		return "", utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	content := choice.Message.Content[0].Text
	if content == "" {
		s.logger.Error("Empty text content in DashScope response", nil,
			"trace_id", traceID,
		)
		return "", utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	return content, nil
}

// extractQuestType 提取题目类型
func (s *qwenParserService) extractQuestType(qutext string, traceID string) (string, error) {
	// 定义有效的题目类型
	validTypes := []string{"判断题", "单选题", "多选题"}
	
	for _, questType := range validTypes {
		if strings.Contains(qutext, questType) {
			s.logger.Info("Quest type extracted successfully",
				"trace_id", traceID,
				"quest_type", questType,
			)
			return questType, nil
		}
	}

	s.logger.Error("Failed to extract valid quest type", nil,
		"trace_id", traceID,
		"qutext", qutext,
	)
	return "", utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
}

// validateAndExtractOptions 验证和提取选项
func (s *qwenParserService) validateAndExtractOptions(options map[string]string, questType string, traceID string) (map[string]string, error) {
	expectedCount := 0
	switch questType {
	case "判断题":
		expectedCount = 2
	case "单选题", "多选题":
		expectedCount = 4
	default:
		return nil, utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	if len(options) != expectedCount {
		s.logger.Error("Invalid options count", nil,
			"trace_id", traceID,
			"quest_type", questType,
			"expected_count", expectedCount,
			"actual_count", len(options),
		)
		return nil, utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	s.logger.Info("Options validated successfully",
		"trace_id", traceID,
		"quest_type", questType,
		"options_count", len(options),
	)

	return options, nil
}

// cleanQuestContent 清洗题目正文
func (s *qwenParserService) cleanQuestContent(qutext string, traceID string) (string, error) {
	// 使用S1文档指定的正则表达式，添加(?s)标志支持多行模式（让.匹配换行符）
	re := regexp.MustCompile(`(?s)^[（(]?(单选题|多选题|判断题)[）)]?(\d+)[、.，,：:]?(.*)$`)

	matches := re.FindStringSubmatch(qutext)
	if len(matches) < 4 {
		s.logger.Error("Failed to clean quest content with regex", nil,
			"trace_id", traceID,
			"qutext", qutext,
		)
		return "", utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	// 提取题干内容（第4个匹配组）
	questContent := strings.TrimSpace(matches[3])

	// 验证清洗后的题干是否干净（不为空且有实际内容）
	if questContent == "" || len(questContent) < 5 {
		s.logger.Error("Cleaned quest content is not valid", nil,
			"trace_id", traceID,
			"original_qutext", qutext,
			"cleaned_content", questContent,
		)
		return "", utils.NewAPIError(utils.CodeImageProcessFailed, "图片不标准，请正确拍摄！")
	}

	s.logger.Info("Quest content cleaned successfully",
		"trace_id", traceID,
		"original_qutext", qutext,
		"cleaned_content", questContent,
	)

	return questContent, nil
}

// GenerateCacheKey 生成缓存键（按照S1文档要求）
func (s *qwenParserService) GenerateCacheKey(questType, questContent string, questOptions map[string]string, traceID string) (string, error) {
	s.logger.Info("Starting cache key generation",
		"trace_id", traceID,
		"quest_type", questType,
		"quest_content", questContent,
		"options_count", len(questOptions),
	)

	// 1. 将quest_type、quest_content、quest_option取出纯文本拼接到一起
	var cacheKeyBuilder strings.Builder

	// 添加题目类型
	cacheKeyBuilder.WriteString(questType)

	// 添加题目内容
	cacheKeyBuilder.WriteString(questContent)

	// 根据题目类型添加选项
	if questType == "判断题" {
		// 判断题只有两个选项
		for _, key := range []string{"A", "B"} {
			if option, exists := questOptions[key]; exists {
				cacheKeyBuilder.WriteString(option)
			}
		}
	} else {
		// 单选题和多选题有四个选项
		for _, key := range []string{"A", "B", "C", "D"} {
			if option, exists := questOptions[key]; exists {
				cacheKeyBuilder.WriteString(option)
			}
		}
	}

	rawCacheKey := cacheKeyBuilder.String()

	s.logger.Info("Raw cache key generated",
		"trace_id", traceID,
		"raw_key_length", len(rawCacheKey),
	)

	// 2. 使用正则清洗空格换行符以及标点符号
	cleanRegex := regexp.MustCompile(`[\s\p{P}\x{3000}]+`)
	cleanedCacheKey := cleanRegex.ReplaceAllString(rawCacheKey, "")

	// 验证清洗后的缓存键是否有效
	if cleanedCacheKey == "" || len(cleanedCacheKey) < 10 {
		s.logger.Error("Generated cache key is invalid", nil,
			"trace_id", traceID,
			"raw_key", rawCacheKey,
			"cleaned_key", cleanedCacheKey,
		)
		return "", utils.NewAPIError(utils.CodeImageProcessFailed, "缓存键生成失败")
	}

	s.logger.Info("Cache key generation completed successfully",
		"trace_id", traceID,
		"raw_key_length", len(rawCacheKey),
		"cleaned_key_length", len(cleanedCacheKey),
	)

	return cleanedCacheKey, nil
}
