package service

import (
	"context"
	"zhengshi/internal/config"
	"zhengshi/internal/utils"
	"zhengshi/pkg/client"
)

// QwenService Qwen服务接口
type QwenService interface {
	AnalyzeImage(ctx context.Context, imageURL, traceID string) (string, error)
	AnalyzeWithQwenPlus(ctx context.Context, questType, questContent string, questOptions map[string]string, traceID string) (string, error)
}

// qwenService Qwen服务实现
type qwenService struct {
	client client.QwenClient
	logger utils.Logger
}

// NewQwenService 创建Qwen服务
func NewQwenService(cfg config.QwenConfig, logger utils.Logger) QwenService {
	qwenClient := client.NewQwenClient(cfg, logger)
	return &qwenService{
		client: qwenClient,
		logger: logger,
	}
}

// AnalyzeImage 分析图片
func (s *qwenService) AnalyzeImage(ctx context.Context, imageURL, traceID string) (string, error) {
	s.logger.Info("Starting Qwen image analysis",
		"trace_id", traceID,
		"image_url", imageURL,
	)

	result, err := s.client.AnalyzeImage(ctx, imageURL)
	if err != nil {
		s.logger.Error("Qwen image analysis failed", err,
			"trace_id", traceID,
			"image_url", imageURL,
		)
		return "", utils.NewAPIError(utils.CodeInternalError, err.Error())
	}

	s.logger.Info("Qwen image analysis completed",
		"trace_id", traceID,
		"image_url", imageURL,
	)

	return result, nil
}

// AnalyzeWithQwenPlus 使用qwen-plus模型分析题目
func (s *qwenService) AnalyzeWithQwenPlus(ctx context.Context, questType, questContent string, questOptions map[string]string, traceID string) (string, error) {
	s.logger.Info("Starting Qwen-plus analysis",
		"trace_id", traceID,
		"quest_type", questType,
		"quest_content", questContent,
		"options_count", len(questOptions),
	)

	result, err := s.client.AnalyzeWithQwenPlus(ctx, questType, questContent, questOptions)
	if err != nil {
		s.logger.Error("Qwen-plus analysis failed", err,
			"trace_id", traceID,
			"quest_type", questType,
		)
		return "", utils.NewAPIError(utils.CodeInternalError, err.Error())
	}

	s.logger.Info("Qwen-plus analysis completed",
		"trace_id", traceID,
		"quest_type", questType,
	)

	return result, nil
}
