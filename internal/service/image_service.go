package service

import (
	"context"

	"zhengshi/internal/model"
	"zhengshi/internal/repository"
	"zhengshi/internal/utils"
)

// ImageService 图片服务接口
type ImageService interface {
	// 使用Qwen分析图片并解析数据
	AnalyzeImageWithQwen(ctx context.Context, imageURL, traceID string) (*model.ProcessContext, error)
	// 使用Qwen-plus分析题目
	AnalyzeWithQwenPlus(ctx context.Context, processCtx *model.ProcessContext) (*model.ProcessContext, error)
	// 获取分析结果
	GetAnalysisResult(ctx context.Context, id uint) (*model.ImageAnalysisResponse, error)
}

// imageService 图片服务实现
type imageService struct {
	imageRepo         repository.ImageRepository
	qwenService       QwenService
	qwenParserService QwenParserService
	deepSeekService   DeepSeekService
	cache             utils.RedisClient
	logger            utils.Logger
}

// NewImageService 创建图片服务
func NewImageService(
	imageRepo repository.ImageRepository,
	qwenService QwenService,
	qwenParserService QwenParserService,
	deepSeekService DeepSeekService,
	cache utils.RedisClient,
	logger utils.Logger,
) ImageService {
	return &imageService{
		imageRepo:         imageRepo,
		qwenService:       qwenService,
		qwenParserService: qwenParserService,
		deepSeekService:   deepSeekService,
		cache:             cache,
		logger:            logger,
	}
}

// AnalyzeImageWithQwen 使用Qwen分析图片并解析数据
func (s *imageService) AnalyzeImageWithQwen(ctx context.Context, imageURL, traceID string) (*model.ProcessContext, error) {
	s.logger.Info("Starting Qwen image analysis and parsing",
		"trace_id", traceID,
		"image_url", imageURL,
	)

	// 创建ProcessContext
	processCtx := model.NewProcessContext(traceID, imageURL)
	processCtx.Status = "processing"
	processCtx.CurrentStep = "正在调用Qwen模型分析图片"

	// 1. 调用Qwen服务分析图片
	qwenRawData, err := s.qwenService.AnalyzeImage(ctx, imageURL, traceID)
	if err != nil {
		s.logger.Error("Qwen image analysis failed", err,
			"trace_id", traceID,
			"image_url", imageURL,
		)
		processCtx.Status = "failed"
		processCtx.CurrentStep = "Qwen模型调用失败"
		return processCtx, err
	}

	// 保存Qwen原始数据
	processCtx.QwenRawData = qwenRawData
	processCtx.Status = "qwen_completed"
	processCtx.CurrentStep = "Qwen模型分析完成，开始解析数据"

	s.logger.Info("Qwen image analysis completed, starting data parsing",
		"trace_id", traceID,
		"image_url", imageURL,
		"raw_data_length", len(qwenRawData),
	)

	// 2. 解析Qwen返回的数据
	parsedResult, err := s.qwenParserService.ParseQwenData(qwenRawData, traceID)
	if err != nil {
		s.logger.Error("Qwen data parsing failed", err,
			"trace_id", traceID,
			"image_url", imageURL,
		)
		processCtx.Status = "failed"
		processCtx.CurrentStep = "数据解析失败"
		return processCtx, err
	}

	// 保存解析结果到ProcessContext
	processCtx.QuestType = parsedResult.QuestType
	processCtx.QuestOption = parsedResult.QuestOption
	processCtx.QuestContent = parsedResult.QuestContent
	processCtx.Status = "parsing_completed"
	processCtx.CurrentStep = "数据解析完成，开始生成缓存键"

	s.logger.Info("Qwen data parsing completed successfully",
		"trace_id", traceID,
		"image_url", imageURL,
		"quest_type", parsedResult.QuestType,
		"quest_content", parsedResult.QuestContent,
		"options_count", len(parsedResult.QuestOption),
	)

	// 3. 生成缓存键（按照S1文档要求）
	cacheKey, err := s.qwenParserService.GenerateCacheKey(
		parsedResult.QuestType,
		parsedResult.QuestContent,
		parsedResult.QuestOption,
		traceID,
	)
	if err != nil {
		s.logger.Error("Cache key generation failed", err,
			"trace_id", traceID,
			"image_url", imageURL,
		)
		processCtx.Status = "failed"
		processCtx.CurrentStep = "缓存键生成失败"
		return processCtx, err
	}

	// 保存缓存键到ProcessContext
	processCtx.CacheKey = cacheKey
	processCtx.Status = "cache_key_generated"
	processCtx.CurrentStep = "缓存键生成完成"

	s.logger.Info("Cache key generation completed successfully",
		"trace_id", traceID,
		"image_url", imageURL,
		"cache_key", cacheKey,
		"cache_key_length", len(cacheKey),
	)

	return processCtx, nil
}

// AnalyzeWithQwenPlus 使用Qwen-plus分析题目（按照S1文档要求）
func (s *imageService) AnalyzeWithQwenPlus(ctx context.Context, processCtx *model.ProcessContext) (*model.ProcessContext, error) {
	s.logger.Info("Starting Qwen-plus analysis",
		"trace_id", processCtx.TraceID,
		"image_url", processCtx.ImageURL,
		"quest_type", processCtx.QuestType,
		"quest_content", processCtx.QuestContent,
		"options_count", len(processCtx.QuestOption),
	)

	// 更新ProcessContext状态
	processCtx.Status = "qwen_plus_processing"
	processCtx.CurrentStep = "正在调用Qwen-plus模型分析题目"

	// 调用Qwen-plus服务分析题目
	qwenPlusResult, err := s.qwenService.AnalyzeWithQwenPlus(
		ctx,
		processCtx.QuestType,
		processCtx.QuestContent,
		processCtx.QuestOption,
		processCtx.TraceID,
	)
	if err != nil {
		s.logger.Error("Qwen-plus analysis failed", err,
			"trace_id", processCtx.TraceID,
			"image_url", processCtx.ImageURL,
			"quest_type", processCtx.QuestType,
		)
		processCtx.Status = "failed"
		processCtx.CurrentStep = "Qwen-plus模型调用失败"
		return processCtx, err
	}

	// 保存Qwen-plus结果到ProcessContext
	processCtx.QwenPlusResult = qwenPlusResult
	processCtx.Status = "qwen_plus_completed"
	processCtx.CurrentStep = "Qwen-plus模型分析完成"

	s.logger.Info("Qwen-plus analysis completed successfully",
		"trace_id", processCtx.TraceID,
		"image_url", processCtx.ImageURL,
		"quest_type", processCtx.QuestType,
		"result_length", len(qwenPlusResult),
	)

	return processCtx, nil
}

// GetAnalysisResult 获取分析结果
func (s *imageService) GetAnalysisResult(ctx context.Context, id uint) (*model.ImageAnalysisResponse, error) {
	analysis, err := s.imageRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get analysis result", err, "analysis_id", id)
		return nil, utils.NewAPIError(utils.CodeNotFound, "分析结果不存在")
	}

	return &model.ImageAnalysisResponse{
		ID:        analysis.ID,
		ImageURL:  analysis.ImageURL,
		CreatedAt: analysis.CreatedAt,
	}, nil
}


