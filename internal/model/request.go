package model

import "time"

// ImageAnalysisRequest 图片分析请求结构
type ImageAnalysisRequest struct {
	ImageURL string `json:"image_url" binding:"required" example:"https://example.com/image.jpg"`
}

// ProcessContext 贯穿整个处理流程的上下文结构体
type ProcessContext struct {
	TraceID         string    `json:"trace_id"`          // 追踪ID
	ImageURL        string    `json:"image_url"`         // 图片URL
	StartTime       time.Time `json:"start_time"`        // 开始处理时间
	Status          string    `json:"status"`            // 当前状态
	CurrentStep     string    `json:"current_step"`      // 当前步骤描述
	QwenRawData     string    `json:"qwen_raw_data"`     // Qwen返回的原始数据，用于后续业务复用
	QwenParsedData  string    `json:"qwen_parsed_data"`  // Qwen解析后的数据
	CacheKey        string    `json:"cache_key"`         // 基于解析数据生成的缓存键

	// S1文档新增：Qwen数据拆分字段
	QuestType       string                 `json:"quest_type"`    // 题目类型：判断题/单选题/多选题
	QuestOption     map[string]string      `json:"quest_option"`  // 题目选项
	QuestContent    string                 `json:"quest_content"` // 题目正文（清洗后）

	// S1文档最新新增：Qwen-plus分支结果
	QwenPlusResult  string                 `json:"qwen_plus_result"` // Qwen-plus模型返回的原始数据
}

// NewProcessContext 创建新的处理上下文
func NewProcessContext(traceID, imageURL string) *ProcessContext {
	return &ProcessContext{
		TraceID:     traceID,
		ImageURL:    imageURL,
		StartTime:   time.Now(),
		Status:      "received",
		CurrentStep: "请求已接收",
	}
}

// QwenDashScopeRequest DashScope格式的Qwen请求结构（按照S1文档要求）
type QwenDashScopeRequest struct {
	Input      QwenInput      `json:"input"`
	Model      string         `json:"model"`
	Parameters QwenParameters `json:"parameters"`
}

// QwenInput Qwen输入结构
type QwenInput struct {
	Messages []QwenDashScopeMessage `json:"messages"`
}

// QwenDashScopeMessage DashScope格式的Qwen消息结构
type QwenDashScopeMessage struct {
	Role    string                    `json:"role"`
	Content []QwenDashScopeContent    `json:"content"`
}

// QwenDashScopeContent DashScope格式的Qwen内容结构
type QwenDashScopeContent struct {
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

// QwenParameters Qwen请求参数
type QwenParameters struct {
	PresencePenalty   int                    `json:"presence_penalty"`
	RepetitionPenalty int                    `json:"repetition_penalty"`
	ResponseFormat    QwenResponseFormat     `json:"response_format"`
	Temperature       float64                `json:"temperature"`
	TopK              int                    `json:"top_k"`
	TopP              float64                `json:"top_p"`
}

// QwenResponseFormat Qwen响应格式
type QwenResponseFormat struct {
	Type string `json:"type"`
}

// DeepSeekRequest DeepSeek模型请求结构
type DeepSeekRequest struct {
	Model       string          `json:"model"`
	Messages    []DeepSeekMessage `json:"messages"`
	Temperature float64         `json:"temperature,omitempty"`
	MaxTokens   int             `json:"max_tokens,omitempty"`
	Stream      bool            `json:"stream"`
}

// DeepSeekMessage DeepSeek消息结构
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}
