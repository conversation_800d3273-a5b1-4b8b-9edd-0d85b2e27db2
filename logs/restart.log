[2025-06-13 21:17:49] 🔄 开始重启服务...
[2025-06-13 21:17:49] 🔍 查找并停止所有相关进程...
[2025-06-13 21:17:49] 发现占用端口 8080 的进程: 17821
[2025-06-13 21:17:49] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-13 21:17:51] SUCCESS: 进程清理完成
[2025-06-13 21:17:51] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 21:17:51] SUCCESS: 项目文件检查通过
[2025-06-13 21:17:51] 📦 更新Go依赖...
[2025-06-13 21:17:51] SUCCESS: 依赖更新成功
[2025-06-13 21:17:51] 🔨 编译检查...
[2025-06-13 21:17:53] SUCCESS: 编译检查通过
[2025-06-13 21:17:53] 🚀 启动服务...
[2025-06-13 21:17:53] 服务已启动，PID: 18007
[2025-06-13 21:17:53] ⏳ 等待服务启动...
[2025-06-13 21:17:54] SUCCESS: 服务启动成功！
[2025-06-13 21:17:54] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-13 21:17:54] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-13 21:17:54] 📊 服务状态:
[2025-06-13 21:17:54] SUCCESS: 🎉 服务重启完成！
[2025-06-13 21:20:53] 🔄 开始重启服务...
[2025-06-13 21:20:53] 🔍 查找并停止所有相关进程...
[2025-06-13 21:20:53] 发现占用端口 8080 的进程: 17876
18030
[2025-06-13 21:20:53] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-13 21:20:55] SUCCESS: 进程清理完成
[2025-06-13 21:20:55] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 21:20:55] SUCCESS: 项目文件检查通过
[2025-06-13 21:20:55] 📦 更新Go依赖...
[2025-06-13 21:20:55] SUCCESS: 依赖更新成功
[2025-06-13 21:20:55] 🔨 编译检查...
[2025-06-13 21:20:56] SUCCESS: 编译检查通过
[2025-06-13 21:20:56] 🚀 启动服务...
[2025-06-13 21:20:56] 服务已启动，PID: 18437
[2025-06-13 21:20:56] ⏳ 等待服务启动...
[2025-06-13 21:20:58] SUCCESS: 服务启动成功！
[2025-06-13 21:20:58] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-13 21:20:58] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-13 21:20:58] 📊 服务状态:
[2025-06-13 21:20:58] SUCCESS: 🎉 服务重启完成！
[2025-06-13 21:51:26] 🔄 开始重启服务...
[2025-06-13 21:51:26] 🔍 查找并停止所有相关进程...
[2025-06-13 21:51:26] 发现占用端口 8080 的进程: 22899
[2025-06-13 21:51:26] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-13 21:51:28] SUCCESS: 进程清理完成
[2025-06-13 21:51:28] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 21:51:28] SUCCESS: 项目文件检查通过
[2025-06-13 21:51:28] 📦 更新Go依赖...
[2025-06-13 21:51:28] SUCCESS: 依赖更新成功
[2025-06-13 21:51:28] 🔨 编译检查...
[2025-06-13 21:51:30] SUCCESS: 编译检查通过
[2025-06-13 21:51:30] 🚀 启动服务...
[2025-06-13 21:51:30] 服务已启动，PID: 23050
[2025-06-13 21:51:30] ⏳ 等待服务启动...
[2025-06-13 21:51:31] SUCCESS: 服务启动成功！
[2025-06-13 21:51:31] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-13 21:51:31] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-13 21:51:31] 📊 服务状态:
[2025-06-13 21:51:31] SUCCESS: 🎉 服务重启完成！
[2025-06-13 21:54:46] 🔄 开始重启服务...
[2025-06-13 21:54:46] 🔍 查找并停止所有相关进程...
[2025-06-13 21:54:46] 发现占用端口 8080 的进程: 23072
[2025-06-13 21:54:46] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-13 21:54:48] SUCCESS: 进程清理完成
[2025-06-13 21:54:48] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 21:54:48] SUCCESS: 项目文件检查通过
[2025-06-13 21:54:48] 📦 更新Go依赖...
[2025-06-13 21:54:48] SUCCESS: 依赖更新成功
[2025-06-13 21:54:48] 🔨 编译检查...
[2025-06-13 21:54:49] SUCCESS: 编译检查通过
[2025-06-13 21:54:49] 🚀 启动服务...
[2025-06-13 21:54:49] 服务已启动，PID: 23663
[2025-06-13 21:54:49] ⏳ 等待服务启动...
[2025-06-13 21:54:51] SUCCESS: 服务启动成功！
[2025-06-13 21:54:51] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-13 21:54:51] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-13 21:54:51] 📊 服务状态:
[2025-06-13 21:54:51] SUCCESS: 🎉 服务重启完成！
[2025-06-13 22:05:07] 🔄 开始重启服务...
[2025-06-13 22:05:07] 🔍 查找并停止所有相关进程...
[2025-06-13 22:05:07] 发现占用端口 8080 的进程: 22132
23688
[2025-06-13 22:05:07] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-13 22:05:09] SUCCESS: 进程清理完成
[2025-06-13 22:05:09] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 22:05:09] SUCCESS: 项目文件检查通过
[2025-06-13 22:05:09] 📦 更新Go依赖...
[2025-06-13 22:05:09] SUCCESS: 依赖更新成功
[2025-06-13 22:05:09] 🔨 编译检查...
[2025-06-13 22:05:10] SUCCESS: 编译检查通过
[2025-06-13 22:05:10] 🚀 启动服务...
[2025-06-13 22:05:10] 服务已启动，PID: 25017
[2025-06-13 22:05:10] ⏳ 等待服务启动...
[2025-06-13 22:05:12] SUCCESS: 服务启动成功！
[2025-06-13 22:05:12] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-13 22:05:12] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-13 22:05:12] 📊 服务状态:
[2025-06-13 22:05:12] SUCCESS: 🎉 服务重启完成！
[2025-06-13 22:32:06] 🔄 开始重启服务...
[2025-06-13 22:32:06] 🔍 查找并停止所有相关进程...
[2025-06-13 22:32:06] 发现占用端口 8080 的进程: 25041
25042
[2025-06-13 22:32:06] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-13 22:32:08] SUCCESS: 进程清理完成
[2025-06-13 22:32:08] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 22:32:08] SUCCESS: 项目文件检查通过
[2025-06-13 22:32:08] 📦 更新Go依赖...
[2025-06-13 22:32:08] SUCCESS: 依赖更新成功
[2025-06-13 22:32:08] 🔨 编译检查...
[2025-06-13 22:32:08] ERROR: 编译失败，请检查代码
[2025-06-13 22:33:48] 🔄 开始重启服务...
[2025-06-13 22:33:48] 🔍 查找并停止所有相关进程...
[2025-06-13 22:33:50] SUCCESS: 进程清理完成
[2025-06-13 22:33:50] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 22:33:50] SUCCESS: 项目文件检查通过
[2025-06-13 22:33:50] 📦 更新Go依赖...
[2025-06-13 22:33:50] SUCCESS: 依赖更新成功
[2025-06-13 22:33:50] 🔨 编译检查...
[2025-06-13 22:33:50] SUCCESS: 编译检查通过
[2025-06-13 22:33:50] 🚀 启动服务...
[2025-06-13 22:33:50] 服务已启动，PID: 27316
[2025-06-13 22:33:50] ⏳ 等待服务启动...
[2025-06-13 22:33:52] SUCCESS: 服务启动成功！
[2025-06-13 22:33:52] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-13 22:33:52] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-13 22:33:52] 📊 服务状态:
[2025-06-13 22:33:53] SUCCESS: 🎉 服务重启完成！
[2025-06-14 00:21:06] 🔄 开始重启服务...
[2025-06-14 00:21:06] 🔍 查找并停止所有相关进程...
[2025-06-14 00:21:06] 发现占用端口 8080 的进程: 27964
34560
[2025-06-14 00:21:06] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-14 00:21:08] SUCCESS: 进程清理完成
[2025-06-14 00:21:08] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-14 00:21:08] SUCCESS: 项目文件检查通过
[2025-06-14 00:21:08] 📦 更新Go依赖...
[2025-06-14 00:21:08] SUCCESS: 依赖更新成功
[2025-06-14 00:21:08] 🔨 编译检查...
[2025-06-14 00:21:09] SUCCESS: 编译检查通过
[2025-06-14 00:21:09] 🚀 启动服务...
[2025-06-14 00:21:09] 服务已启动，PID: 36720
[2025-06-14 00:21:09] ⏳ 等待服务启动...
[2025-06-14 00:21:11] SUCCESS: 服务启动成功！
[2025-06-14 00:21:11] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-14 00:21:11] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-14 00:21:11] 📊 服务状态:
[2025-06-14 00:21:11] SUCCESS: 🎉 服务重启完成！
