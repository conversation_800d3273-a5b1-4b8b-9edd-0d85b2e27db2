使用golang开发一个web的api服务；
框架要求gin；
mysql8；
redis；

用户携带一个图片url请求api；

api将图片url提交给qwen模型；

得到qwen响应数据后，对其进行格式化解析；

将解析后的数据制作缓存键名称；

将解析后的数据请求给deepseek模型；

将deepseek模型响应数据与解析后的部分qwen数据进行合并存入数据库并返回给用户；


业务要求；
1. 高标准、高规范化开发；
2. 尽可能的将业务模块封装成独立的方法，以便于后续业务功能扩展；
3. 规范化的目录结构；
4. 规范命名；
5. 规范的错误码；
6. 完善的调试日志；


---------------------------------------------------

### mysql与redis的配置信息



MySQL_8数据库
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=t_solve_go_api
MYSQL_CHARSET=utf8mb4

REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=Suyan15913..
REDIS_DB=0

### qen与deepseek的key信息

QWEN_KEY=***********************************	
DEEPSEEK_KEY=***********************************


----------------------------------------------------

先让我们来进行第一步开发；

用户请求并携带一个图片url；

成功接收到请求，返回code200的状态；

从现在开始我需要一个贯穿本次进程的结构体，用来存储我需要的值，比如用户的这个图片url地址；


-----------------------------------------------------

让我们开始处理qwen的相关业务；

1. 将用户请求过来的图片url提交给qwen-vl-plus模型；

- DashScope模式请求；请求地址为；https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation

- 请求体如下；

{
  "input": {
    "messages": [
      {
        "content": [
          {
            "text": "严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}"
          }
        ],
        "role": "system"
      },
      {
        "content": [
          {
            "image": "用户请求携带的图片url"
          },
          {
            "text": "精准且完整的识别问题,严格按照要求输出的完整json,其中的options字段的格式必须正确,选项字母为键，选项内容为值\"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "response_format": {
      "type": "json_object"
    },
    "temperature": 0.2,
    "top_k": 1,
    "top_p": 0.01
  }
}


- 将qwen返回的原始数据保存到ProcessContext，后面业务中会多次复用。
- 然后将qwen返回的原始数据返回给用户；//不在需要了

-------------------------------------------------------------------

让我们写一个新的方法，来对qwen返回的数据进行拆分；

1. 从qutext字段中取出（题目类型）/判断题/单选题/多选题  一共三种题目类型；
    - 必须提取正确的题型，正确题型只有上述三种，如果未能正确提取题型 则中止进程返回错误：图片不标准，请正确拍摄！
    - 将提取到的题型保存到ProcessContext中；
    - 题型命名：quest_type
2. 从options字段中取出所有选项，并保存到ProcessContext中；
    - 单选或多选，必须保证四个选项，如果选项数量不为4个，则中止进程返回错误：图片不标准，请正确拍摄！
    - 判断题，必须保证两个选项，如果选项数量不为2个，则中止进程返回错误：图片不标准，请正确拍摄！
    - 选项命名：quest_option
3. 从qutext字段中取出（题目正文）并保存到ProcessContext中；
    - 使用这个正则对qutext进行清洗re := regexp.MustCompile(`^[（(]?(单选题|多选题|判断题)[）)]?(\d+)[、.，,：:]?(.*)$`)
    - 清洗后得到干净的题干内容。
    - 将清洗后的题干内容保存到ProcessContext中；
    - 找一个可靠方案来验证清洗后的题干是否干净，如果清洗不干净，则中止进程返回错误：图片不标准，请正确拍摄！
    - 题干命名：quest_content


拆分完成后将ProcessContext返回给用户；

-------------------------------------------------------------------
让我们在写一个缓存键预处理的方法；

1. 将ProcessContext中的quest_type、quest_content、quest_option取出纯文本拼接到一起
    - 示例； quest_type + quest_content + quest_option_A + quest_option_B + quest_option_C + quest_option_D
    - 注意判断题只有两个选项，所以只需要拼接两个选项即可
    
2. 将拼接后的字符串使用正则清洗空格换行符以及标点符号(`[[:space:][:punct:]\u3000]+`)


清洗完成后将ProcessContext返回给用户；

----------------------------------------------------------------------
现在让我们进行下一步开发

这里我需要一个分支，一个是走qwen-plus的分支 一个是走deepseek的分支；我们先来处理qwen-plus的分支；

1. 将ProcessContext中的quest_type + quest_content + quest_option构建一个json提交给qwen3模型；
2. qwen-plus返回的数据返回给用户
3. qwen-plus的key与qwen-vl-plus公用一个；

- qwen-plus请求体示例


{
  "input": {
    "messages": [
      {
        "content": [
          {
            "text": "严格标准返回json格式。示例{\"answer\":\"正确答案\",\"analysis\":{\"答案解析\"}}"
          }
        ],
        "role": "system"
      },
      {
        "content": [
          {
            "text": "解答以下问题给出正确答案与解析【这里是提交给qwen-plus的结构体】\"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "response_format": {
      "type": "json_object"
    },
    "temperature": 0.2,
    "top_k": 1,
    "top_p": 0.01
  }
}
